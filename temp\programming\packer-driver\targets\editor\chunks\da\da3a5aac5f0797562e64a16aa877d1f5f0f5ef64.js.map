{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts"], "names": ["PathBaked", "_decorator", "Component", "PathPoint", "PointBaker", "ccclass", "property", "points", "addPoint", "x", "y", "isOnPath", "push", "removePoint", "index", "length", "splice", "clearPoints", "PathBaker", "type", "serializable", "bake", "baked", "for<PERSON>ach", "point", "pointData"], "mappings": ";;;6HAOaA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAPJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AAGZC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;AAK9B;2BACaD,S,GAAN,MAAMA,SAAN,CAAkC;AAAA;AAAA,eACrCO,MADqC;AAAA;;AAGrCC,QAAAA,QAAQ,CAACC,CAAD,EAAYC,CAAZ,EAAuBC,QAAvB,EAA0C;AAC9C,cAAI,CAAC,KAAKJ,MAAV,EAAkB;AACd,iBAAKA,MAAL,GAAc,EAAd;AACH;;AACD,eAAKA,MAAL,CAAYK,IAAZ,CAAiB;AAAA;AAAA,sCAAcH,CAAd,EAAiBC,CAAjB,EAAoBC,QAApB,CAAjB;AACH;;AAEDE,QAAAA,WAAW,CAACC,KAAD,EAAgB;AACvB,cAAI,KAAKP,MAAL,IAAeO,KAAK,IAAI,CAAxB,IAA6BA,KAAK,GAAG,KAAKP,MAAL,CAAYQ,MAArD,EAA6D;AACzD,iBAAKR,MAAL,CAAYS,MAAZ,CAAmBF,KAAnB,EAA0B,CAA1B;AACH;AACJ;;AAEDG,QAAAA,WAAW,GAAG;AACV,eAAKV,MAAL,GAAc,EAAd;AACH;;AAlBoC,O;;2BAsB5BW,S,WADZb,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAAC;AAAEa,QAAAA,IAAI,EAAE;AAAA;AAAA,qCAAR;AAAsBC,QAAAA,YAAY,EAAE;AAApC,OAAD,C,4BAHb,MACaF,SADb,SAC+BhB,SAD/B,CAEA;AAAA;AAAA;;AAAA;AAAA;;AAIImB,QAAAA,IAAI,GAAW;AACX,gBAAMC,KAAK,GAAG,IAAItB,SAAJ,EAAd;AACA,eAAKO,MAAL,CAAYgB,OAAZ,CAAoBC,KAAK,IAAI;AACzB,gBAAIC,SAAS,GAAGD,KAAK,CAACH,IAAN,EAAhB,CADyB,CAEzB;;AACAC,YAAAA,KAAK,CAACd,QAAN,CAAeiB,SAAS,CAAChB,CAAzB,EAA4BgB,SAAS,CAACf,CAAtC,EAAyCe,SAAS,CAACd,QAAnD;AACH,WAJD;AAKA,iBAAOW,KAAP;AACH;;AAZL,O;;;;;iBAEkC,E", "sourcesContent": ["import { _decorator, Component, Node, ValueType } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\nimport { IBaker, IBaked } from './Baker';\r\nimport { PathPoint } from '../Data/PathPoint';\r\nimport { PointBaker } from './PointBaker';\r\n\r\n// TODO: 导出json\r\nexport class PathBaked implements IBaked {\r\n    points: PathPoint[];\r\n\r\n    addPoint(x: number, y: number, isOnPath: boolean) {\r\n        if (!this.points) {\r\n            this.points = [];\r\n        }\r\n        this.points.push(new PathPoint(x, y, isOnPath));\r\n    }\r\n\r\n    removePoint(index: number) {\r\n        if (this.points && index >= 0 && index < this.points.length) {\r\n            this.points.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    clearPoints() {\r\n        this.points = [];\r\n    }\r\n}\r\n\r\n@ccclass('PathBaker')\r\nexport class PathBaker extends Component implements IBaker \r\n{\r\n    @property({ type: [PointBaker], serializable: true })\r\n    public points: PointBaker[] = [];\r\n\r\n    bake(): IBaked {\r\n        const baked = new PathBaked();\r\n        this.points.forEach(point => {\r\n            let pointData = point.bake() as PathPoint;\r\n            // Ensure pointData is of type PathPoint\r\n            baked.addPoint(pointData.x, pointData.y, pointData.isOnPath);\r\n        });\r\n        return baked;\r\n    }\r\n}\r\n\r\n\r\n"]}