System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, PathPoint, PointBaked, _dec, _class2, _class3, _descriptor, _crd, ccclass, property, PointBaker;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfIBaker(extras) {
    _reporterNs.report("IBaker", "./Baker", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIBaked(extras) {
    _reporterNs.report("IBaked", "./Baker", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../Data/PathPoint", _context.meta, extras);
  }

  _export("PointBaked", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      PathPoint = _unresolved_2.PathPoint;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a11f8BZqM9Esb3rg3Umfv1k", "PointBaker", undefined);

      __checkObsolete__(['_decorator', 'Component', 'CCBoolean']);

      ({
        ccclass,
        property
      } = _decorator);

      // TODO: 导出json
      _export("PointBaked", PointBaked = class PointBaked {
        constructor() {
          this.points = void 0;
        }

      });

      _export("PointBaker", PointBaker = (_dec = ccclass('PointBaker'), _dec(_class2 = (_class3 = class PointBaker extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "isOnPath", _descriptor, this);
        }

        bake() {
          var baked = new PointBaked();
          baked.points = new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(this.node.position.x, this.node.position.y, this.isOnPath);
          return baked;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class3.prototype, "isOnPath", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return true;
        }
      })), _class3)) || _class2));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6513f30e3bfe3f5570d9ea7e8013aa26d4ef44a7.js.map