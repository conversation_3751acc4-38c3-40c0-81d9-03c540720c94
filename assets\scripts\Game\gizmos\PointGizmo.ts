
import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';
import { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';
import { GizmoUtils } from './GizmoUtils';
import { PathPoint } from '../world/level/Data/PathPoint';
import { PointBaker } from '../world/level/Baker/PointBaker';;

@RegisterGizmoDrawer
export class PointGizmo extends GizmoDrawer<PointBaker> {
    
    public readonly componentType = PointBaker;
    public readonly drawerName = "PointGizmo";
    
    public drawGizmos(component: PointBaker, graphics: Graphics, node: Node): void {
        const isOnPath = component.isOnPath;
        
        // draw PointBaker's position as a circle, if isOnPath is true, draw filled circle, otherwise draw an outline
        const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);
        const gizmoX = gizmoPos.x;
        const gizmoY = gizmoPos.y;

        if (isOnPath) {
            this.drawCircle(graphics, gizmoX, gizmoY, 5, Color.GREEN, true);
        } else {
            this.drawCircle(graphics, gizmoX, gizmoY, 5, Color.RED, false);
        }
    }
}