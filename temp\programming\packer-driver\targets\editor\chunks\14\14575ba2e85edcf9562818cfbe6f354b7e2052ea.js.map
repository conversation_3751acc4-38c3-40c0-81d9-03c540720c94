{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts"], "names": ["GizmoDrawer", "RegisterGizmoDrawer", "constructor", "gizmoDrawerRegistry", "push", "getRegisteredGizmoDrawers", "autoRegisterGizmoDrawers", "registerFunction", "DrawerConstructor", "drawer", "error", "console", "name", "Vec3", "Gizmo<PERSON><PERSON>s", "enabled", "canHandle", "component", "componentType", "onRegister", "onUnregister", "getPriority", "worldToGizmoSpace", "worldPos", "gizmoNode", "localPos", "inverseTransformPoint", "x", "y", "z", "drawCross", "graphics", "size", "color", "drawCircle", "radius", "filled", "drawArrow", "startX", "startY", "endX", "endY", "arrowSize", "drawLine", "lineWidth", "drawRect", "width", "height", "drawText", "_graphics", "_text", "_x", "_y", "_color"], "mappings": ";;;iGA2CsBA,W;;AArCtB;AACA;AACA;AACA;AACO,WAASC,mBAAT,CAA8DC,WAA9D,EAAiF;AACpF;AACAC,IAAAA,mBAAmB,CAACC,IAApB,CAAyBF,WAAzB;AAEA,WAAOA,WAAP;AACH;AAED;AACA;AACA;;;AACO,WAASG,yBAAT,GAAmE;AACtE,WAAO,CAAC,GAAGF,mBAAJ,CAAP;AACH;AAED;AACA;AACA;AACA;;;AACO,WAASG,wBAAT,CAAkCC,gBAAlC,EAAyF;AAC5F,SAAK,MAAMC,iBAAX,IAAgCL,mBAAhC,EAAqD;AACjD,UAAI;AACA,cAAMM,MAAM,GAAG,IAAID,iBAAJ,EAAf;AACAD,QAAAA,gBAAgB,CAACE,MAAD,CAAhB;AACH,OAHD,CAGE,OAAOC,KAAP,EAAc;AACZC,QAAAA,OAAO,CAACD,KAAR,CAAe,wCAAuCF,iBAAiB,CAACI,IAAK,GAA7E,EAAiFF,KAAjF;AACH;AACJ;AACJ;AAED;AACA;AACA;AACA;;;;;;;;yBAhCgBT,mB;+BAUAI,yB;8BAQAC,wB;;;;;;;;;;;AA5BuCO,MAAAA,I,OAAAA,I;;AAC9CC,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACMX,MAAAA,mB,GAAoD,E;;6BAuCpCH,W,GAAf,MAAeA,WAAf,CAA4D;AAAA;AAE/D;AACJ;AACA;;AAGI;AACJ;AACA;;AAGI;AACJ;AACA;AAdmE,eAexDe,OAfwD,GAerC,IAfqC;AAAA;AAiB/D;AACJ;AACA;AACA;AACA;AACA;;;AAGI;AACJ;AACA;AACA;AACA;AACWC,QAAAA,SAAS,CAACC,SAAD,EAAuC;AACnD,iBAAOA,SAAS,YAAY,KAAKC,aAAjC;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,UAAU,GAAS,CACtB;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,YAAY,GAAS,CACxB;AACH;AAED;AACJ;AACA;AACA;;;AACWC,QAAAA,WAAW,GAAW;AACzB,iBAAO,CAAP;AACH;AAED;AACJ;AACA;AACA;;;AACcC,QAAAA,iBAAiB,CAACC,QAAD,EAA2BC,SAA3B,EAA4D;AACnF;AACA,gBAAMC,QAAQ,GAAG,IAAIZ,IAAJ,EAAjB;AACAW,UAAAA,SAAS,CAACE,qBAAV,CAAgCD,QAAhC,EAA0CF,QAA1C;AACA,iBAAO,IAAIV,IAAJ,CAASY,QAAQ,CAACE,CAAlB,EAAqBF,QAAQ,CAACG,CAA9B,EAAiCH,QAAQ,CAACI,CAA1C,CAAP;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,SAAS,CAACC,QAAD,EAAqBJ,CAArB,EAAgCC,CAAhC,EAA2CI,IAA3C,EAAyDC,KAAzD,EAA6E;AAC5F;AAAA;AAAA,wCAAWH,SAAX,CAAqBC,QAArB,EAA+BJ,CAA/B,EAAkCC,CAAlC,EAAqCI,IAArC,EAA2CC,KAA3C;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,UAAU,CAACH,QAAD,EAAqBJ,CAArB,EAAgCC,CAAhC,EAA2CO,MAA3C,EAA2DF,KAA3D,EAAyEG,MAAe,GAAG,KAA3F,EAAwG;AACxH;AAAA;AAAA,wCAAWF,UAAX,CAAsBH,QAAtB,EAAgCJ,CAAhC,EAAmCC,CAAnC,EAAsCO,MAAtC,EAA8CF,KAA9C,EAAqDG,MAArD;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,SAAS,CAACN,QAAD,EAAqBO,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFR,KAAjF,EAA+FS,SAAiB,GAAG,CAAnH,EAA4H;AAC3I;AAAA;AAAA,wCAAWL,SAAX,CAAqBN,QAArB,EAA+BO,MAA/B,EAAuCC,MAAvC,EAA+CC,IAA/C,EAAqDC,IAArD,EAA2DR,KAA3D,EAAkES,SAAlE;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,QAAQ,CAACZ,QAAD,EAAqBO,MAArB,EAAqCC,MAArC,EAAqDC,IAArD,EAAmEC,IAAnE,EAAiFR,KAAjF,EAA+FW,SAAiB,GAAG,CAAnH,EAA4H;AAC1I;AAAA;AAAA,wCAAWD,QAAX,CAAoBZ,QAApB,EAA8BO,MAA9B,EAAsCC,MAAtC,EAA8CC,IAA9C,EAAoDC,IAApD,EAA0DR,KAA1D,EAAiEW,SAAjE;AACH;AAED;AACJ;AACA;;;AACcC,QAAAA,QAAQ,CAACd,QAAD,EAAqBJ,CAArB,EAAgCC,CAAhC,EAA2CkB,KAA3C,EAA0DC,MAA1D,EAA0Ed,KAA1E,EAAwFG,MAAe,GAAG,KAA1G,EAAuH;AACrI;AAAA;AAAA,wCAAWS,QAAX,CAAoBd,QAApB,EAA8BJ,CAA9B,EAAiCC,CAAjC,EAAoCkB,KAApC,EAA2CC,MAA3C,EAAmDd,KAAnD,EAA0DG,MAA1D;AACH;AAED;AACJ;AACA;AACA;;;AACcY,QAAAA,QAAQ,CAACC,SAAD,EAAsBC,KAAtB,EAAqCC,EAArC,EAAiDC,EAAjD,EAA6DC,MAA7D,EAAkF,CAChG;AACA;AACH;;AA/G8D,O", "sourcesContent": ["import { _decorator, Component, Graphics, Color, Node, Vec3, Vec2 } from 'cc';\nimport { GizmoUtils } from './GizmoUtils';\n\n// Registry for auto-registration\nconst gizmoDrawerRegistry: Array<new () => GizmoDrawer> = [];\n\n/**\n * Decorator to automatically register a gizmo drawer\n * Usage: @RegisterGizmoDrawer class MyGizmo extends GizmoDrawer { ... }\n */\nexport function RegisterGizmoDrawer<T extends new () => GizmoDrawer>(constructor: T): T {\n    // Add to registry for auto-registration\n    gizmoDrawerRegistry.push(constructor);\n\n    return constructor;\n}\n\n/**\n * Get all registered gizmo drawer constructors\n */\nexport function getRegisteredGizmoDrawers(): Array<new () => GizmoDrawer> {\n    return [...gizmoDrawerRegistry];\n}\n\n/**\n * Auto-register all decorated gizmo drawers to a GizmoManager\n * This function should be called from GizmoManager to avoid circular dependencies\n */\nexport function autoRegisterGizmoDrawers(registerFunction: (drawer: GizmoDrawer) => void): void {\n    for (const DrawerConstructor of gizmoDrawerRegistry) {\n        try {\n            const drawer = new DrawerConstructor();\n            registerFunction(drawer);\n        } catch (error) {\n            console.error(`Failed to auto-register gizmo drawer ${DrawerConstructor.name}:`, error);\n        }\n    }\n}\n\n/**\n * Abstract base class for drawing gizmos for specific component types\n * This is not a component itself, but a drawer that can be registered to GizmoManager\n */\nexport abstract class GizmoDrawer<T extends Component = Component> {\n    \n    /**\n     * The component type this drawer handles\n     */\n    public abstract readonly componentType: new (...args: any[]) => T;\n    \n    /**\n     * Name of this gizmo drawer for debugging\n     */\n    public abstract readonly drawerName: string;\n    \n    /**\n     * Whether this drawer is enabled\n     */\n    public enabled: boolean = true;\n    \n    /**\n     * Draw gizmos for the given component\n     * @param component The component to draw gizmos for\n     * @param graphics The graphics component to draw with\n     * @param node The node that contains the component\n     */\n    public abstract drawGizmos(component: T, graphics: Graphics, node: Node): void;\n    \n    /**\n     * Check if this drawer can handle the given component\n     * @param component The component to check\n     * @returns true if this drawer can handle the component\n     */\n    public canHandle(component: Component): component is T {\n        return component instanceof this.componentType;\n    }\n    \n    /**\n     * Called when the drawer is registered to the manager\n     * Override this to perform any initialization\n     */\n    public onRegister(): void {\n        // Override in subclasses if needed\n    }\n    \n    /**\n     * Called when the drawer is unregistered from the manager\n     * Override this to perform any cleanup\n     */\n    public onUnregister(): void {\n        // Override in subclasses if needed\n    }\n    \n    /**\n     * Get the priority of this drawer (higher priority draws last/on top)\n     * Override this to change drawing order\n     */\n    public getPriority(): number {\n        return 0;\n    }\n    \n    /**\n     * Convert world position to gizmo graphics coordinate space\n     * For 2D projects, this converts world coordinates to the local space of the graphics node\n     */\n    protected worldToGizmoSpace(worldPos: Readonly<Vec3>, gizmoNode: Node): Readonly<Vec3> {\n        // Convert world position to local position of the gizmo graphics node\n        const localPos = new Vec3();\n        gizmoNode.inverseTransformPoint(localPos, worldPos);\n        return new Vec3(localPos.x, localPos.y, localPos.z);\n    }\n\n    /**\n     * Helper method to draw a cross at the given position\n     */\n    protected drawCross(graphics: Graphics, x: number, y: number, size: number, color: Color): void {\n        GizmoUtils.drawCross(graphics, x, y, size, color);\n    }\n\n    /**\n     * Helper method to draw a circle\n     */\n    protected drawCircle(graphics: Graphics, x: number, y: number, radius: number, color: Color, filled: boolean = false): void {\n        GizmoUtils.drawCircle(graphics, x, y, radius, color, filled);\n    }\n\n    /**\n     * Helper method to draw an arrow\n     */\n    protected drawArrow(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, arrowSize: number = 8): void {\n        GizmoUtils.drawArrow(graphics, startX, startY, endX, endY, color, arrowSize);\n    }\n\n    /**\n     * Helper method to draw a line\n     */\n    protected drawLine(graphics: Graphics, startX: number, startY: number, endX: number, endY: number, color: Color, lineWidth: number = 1): void {\n        GizmoUtils.drawLine(graphics, startX, startY, endX, endY, color, lineWidth);\n    }\n\n    /**\n     * Helper method to draw a rectangle\n     */\n    protected drawRect(graphics: Graphics, x: number, y: number, width: number, height: number, color: Color, filled: boolean = false): void {\n        GizmoUtils.drawRect(graphics, x, y, width, height, color, filled);\n    }\n    \n    /**\n     * Helper method to draw text (simple implementation)\n     * Note: For more complex text rendering, consider using Label components\n     */\n    protected drawText(_graphics: Graphics, _text: string, _x: number, _y: number, _color: Color): void {\n        // This is a placeholder - in a real implementation you might want to use Label components\n        // or a more sophisticated text rendering system\n    }\n}\n"]}