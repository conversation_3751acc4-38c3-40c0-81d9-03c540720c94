{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PointGizmo.ts"], "names": ["Color", "GizmoDrawer", "RegisterGizmoDrawer", "PointBaker", "PointGizmo", "componentType", "drawerName", "drawGizmos", "component", "graphics", "node", "isOnPath", "gizmoPos", "worldToGizmoSpace", "worldPosition", "gizmoX", "x", "gizmoY", "y", "drawCircle", "GREEN", "RED"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAC+BA,MAAAA,K,OAAAA,K;;AACtBC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,mB,iBAAAA,mB;;AAGbC,MAAAA,U,iBAAAA,U;;;;;;;;;AAAoD;;4BAGhDC,U;;oEADb,MACaA,UADb;AAAA;AAAA,sCACwD;AAAA;AAAA;AAAA,eAEpCC,aAFoC;AAAA;AAAA;AAAA,eAGpCC,UAHoC,GAGvB,YAHuB;AAAA;;AAK7CC,QAAAA,UAAU,CAACC,SAAD,EAAwBC,QAAxB,EAA4CC,IAA5C,EAA8D;AAC3E,cAAMC,QAAQ,GAAGH,SAAS,CAACG,QAA3B,CAD2E,CAG3E;;AACA,cAAMC,QAAQ,GAAG,KAAKC,iBAAL,CAAuBH,IAAI,CAACI,aAA5B,EAA2CL,QAAQ,CAACC,IAApD,CAAjB;AACA,cAAMK,MAAM,GAAGH,QAAQ,CAACI,CAAxB;AACA,cAAMC,MAAM,GAAGL,QAAQ,CAACM,CAAxB;;AAEA,cAAIP,QAAJ,EAAc;AACV,iBAAKQ,UAAL,CAAgBV,QAAhB,EAA0BM,MAA1B,EAAkCE,MAAlC,EAA0C,CAA1C,EAA6CjB,KAAK,CAACoB,KAAnD,EAA0D,IAA1D;AACH,WAFD,MAEO;AACH,iBAAKD,UAAL,CAAgBV,QAAhB,EAA0BM,MAA1B,EAAkCE,MAAlC,EAA0C,CAA1C,EAA6CjB,KAAK,CAACqB,GAAnD,EAAwD,KAAxD;AACH;AACJ;;AAlBmD,O", "sourcesContent": ["\r\nimport { _decorator, Graphics, Color, Node, Vec3 } from 'cc';\r\nimport { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';\r\nimport { GizmoUtils } from './GizmoUtils';\r\nimport { PathPoint } from '../world/level/Data/PathPoint';\r\nimport { PointBaker } from '../world/level/Baker/PointBaker';;\r\n\r\n@RegisterGizmoDrawer\r\nexport class PointGizmo extends GizmoDrawer<PointBaker> {\r\n    \r\n    public readonly componentType = PointBaker;\r\n    public readonly drawerName = \"PointGizmo\";\r\n    \r\n    public drawGizmos(component: PointBaker, graphics: Graphics, node: Node): void {\r\n        const isOnPath = component.isOnPath;\r\n        \r\n        // draw PointBaker's position as a circle, if isOnPath is true, draw filled circle, otherwise draw an outline\r\n        const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);\r\n        const gizmoX = gizmoPos.x;\r\n        const gizmoY = gizmoPos.y;\r\n\r\n        if (isOnPath) {\r\n            this.drawCircle(graphics, gizmoX, gizmoY, 5, Color.GREEN, true);\r\n        } else {\r\n            this.drawCircle(graphics, gizmoX, gizmoY, 5, Color.RED, false);\r\n        }\r\n    }\r\n}"]}