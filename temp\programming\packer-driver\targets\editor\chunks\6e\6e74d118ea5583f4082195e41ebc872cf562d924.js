System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Color, GizmoDrawer, RegisterGizmoDrawer, PointBaker, _dec, _class, _crd, PointGizmo;

  function _reportPossibleCrUseOfGizmoDrawer(extras) {
    _reporterNs.report("GizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRegisterGizmoDrawer(extras) {
    _reporterNs.report("RegisterGizmoDrawer", "./GizmoDrawer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPointBaker(extras) {
    _reporterNs.report("PointBaker", "../world/level/Baker/PointBaker", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Color = _cc.Color;
    }, function (_unresolved_2) {
      GizmoDrawer = _unresolved_2.GizmoDrawer;
      RegisterGizmoDrawer = _unresolved_2.RegisterGizmoDrawer;
    }, function (_unresolved_3) {
      PointBaker = _unresolved_3.PointBaker;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "12053LikZFF+KSXHjXPSpsv", "PointGizmo", undefined);

      __checkObsolete__(['_decorator', 'Graphics', 'Color', 'Node', 'Vec3']);

      ;

      _export("PointGizmo", PointGizmo = (_dec = _crd && RegisterGizmoDrawer === void 0 ? (_reportPossibleCrUseOfRegisterGizmoDrawer({
        error: Error()
      }), RegisterGizmoDrawer) : RegisterGizmoDrawer, _dec(_class = class PointGizmo extends (_crd && GizmoDrawer === void 0 ? (_reportPossibleCrUseOfGizmoDrawer({
        error: Error()
      }), GizmoDrawer) : GizmoDrawer) {
        constructor(...args) {
          super(...args);
          this.componentType = _crd && PointBaker === void 0 ? (_reportPossibleCrUseOfPointBaker({
            error: Error()
          }), PointBaker) : PointBaker;
          this.drawerName = "PointGizmo";
        }

        drawGizmos(component, graphics, node) {
          const isOnPath = component.isOnPath; // draw PointBaker's position as a circle, if isOnPath is true, draw filled circle, otherwise draw an outline

          const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);
          const gizmoX = gizmoPos.x;
          const gizmoY = gizmoPos.y;

          if (isOnPath) {
            this.drawCircle(graphics, gizmoX, gizmoY, 5, Color.GREEN, true);
          } else {
            this.drawCircle(graphics, gizmoX, gizmoY, 5, Color.RED, false);
          }
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6e74d118ea5583f4082195e41ebc872cf562d924.js.map