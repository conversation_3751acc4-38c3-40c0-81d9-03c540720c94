{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts"], "names": ["_decorator", "ccclass", "property"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;AAE9B;AACA;AACA", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n/*\r\n* 被烘焙的对象接口, 可用于序列化\r\n*/\r\nexport interface IBaked {\r\n}\r\n\r\nexport interface IBaker {\r\n    bake(): IBaked;\r\n}"]}