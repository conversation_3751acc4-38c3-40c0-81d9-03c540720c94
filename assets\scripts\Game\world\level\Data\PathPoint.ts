
import { _decorator, Vec2, CCBoolean } from 'cc';
const { ccclass, property } = _decorator;

// define a point struct with x, y and boolean isOnPath
@ccclass
export class PathPoint {
    @property({ type: Vec2 })
    public position: Vec2;
    // direction is for the unit's rotation for moving along the path
    @property({ type: Vec2 })
    public direction: Vec2;
    @property({ type: CCBoolean })
    public isOnPath: boolean;

    constructor(position: Vec2, direction: Vec2, isOnPath: boolean) {
        this.position = position;
        this.direction = direction;
        this.isOnPath = isOnPath;
    }
}