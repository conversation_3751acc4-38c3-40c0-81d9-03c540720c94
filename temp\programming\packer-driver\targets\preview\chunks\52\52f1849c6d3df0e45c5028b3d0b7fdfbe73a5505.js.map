{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts"], "names": ["Color", "GizmoDrawer", "RegisterGizmoDrawer", "Gizmo<PERSON><PERSON>s", "EmitterArc", "EmitterArcGizmo", "componentType", "drawerName", "showRadius", "showDirections", "showCenter", "showArc", "radiusColor", "GRAY", "directionColor", "RED", "centerColor", "WHITE", "arcColor", "YELLOW", "speedScale", "arrowSize", "centerSize", "drawGizmos", "emitter", "graphics", "node", "gizmoPos", "worldToGizmoSpace", "worldPosition", "gizmoX", "x", "gizmoY", "y", "drawCenter", "radius", "drawRadius", "arc", "drawArcIndicator", "count", "drawDirections", "worldX", "worldY", "drawCross", "drawCircle", "strokeColor", "lineWidth", "baseAngleRad", "angle", "Math", "PI", "arcRad", "startAngle", "endAngle", "arcStartRadius", "baseLength", "speedFactor", "speedMultiplier", "<PERSON><PERSON><PERSON><PERSON>", "max", "arcEndRadius", "segments", "floor", "i", "cos", "sin", "moveTo", "lineTo", "startSpawnX", "startSpawnY", "endSpawnX", "endSpawnY", "startEndX", "startEndY", "endEndX", "endEndY", "stroke", "<PERSON><PERSON><PERSON><PERSON>", "direction", "getDirection", "spawnPos", "getSpawnPosition", "startX", "startY", "endX", "endY", "drawArrow", "getPriority", "configure", "options", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAA+BA,MAAAA,K,OAAAA,K;;AACtBC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,mB,iBAAAA,mB;;AACbC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;AAET;AACA;AACA;AACA;iCAEaC,e;;oEADb,MACaA,eADb;AAAA;AAAA,sCAC6D;AAAA;AAAA;AAAA,eAEzCC,aAFyC;AAAA;AAAA;AAAA,eAGzCC,UAHyC,GAG5B,iBAH4B;AAKzD;AALyD,eAMlDC,UANkD,GAM5B,IAN4B;AAAA,eAOlDC,cAPkD,GAOxB,IAPwB;AAAA,eAQlDC,UARkD,GAQ5B,IAR4B;AAAA,eASlDC,OATkD,GAS/B,IAT+B;AAWzD;AAXyD,eAYlDC,WAZkD,GAY7BZ,KAAK,CAACa,IAZuB;AAAA,eAalDC,cAbkD,GAa1Bd,KAAK,CAACe,GAboB;AAAA,eAclDC,WAdkD,GAc7BhB,KAAK,CAACiB,KAduB;AAAA,eAelDC,QAfkD,GAehClB,KAAK,CAACmB,MAf0B;AAiBzD;AAjByD,eAkBlDC,UAlBkD,GAkB7B,GAlB6B;AAAA,eAmBlDC,SAnBkD,GAmB9B,CAnB8B;AAAA,eAoBlDC,UApBkD,GAoB7B,CApB6B;AAAA;;AAsBlDC,QAAAA,UAAU,CAACC,OAAD,EAAsBC,QAAtB,EAA0CC,IAA1C,EAA4D;AACzE;AACA,cAAMC,QAAQ,GAAG,KAAKC,iBAAL,CAAuBF,IAAI,CAACG,aAA5B,EAA2CJ,QAAQ,CAACC,IAApD,CAAjB;AACA,cAAMI,MAAM,GAAGH,QAAQ,CAACI,CAAxB;AACA,cAAMC,MAAM,GAAGL,QAAQ,CAACM,CAAxB,CAJyE,CAMzE;;AACA,cAAI,KAAKvB,UAAT,EAAqB;AACjB,iBAAKwB,UAAL,CAAgBT,QAAhB,EAA0BK,MAA1B,EAAkCE,MAAlC;AACH,WATwE,CAWzE;;;AACA,cAAI,KAAKxB,UAAL,IAAmBgB,OAAO,CAACW,MAAR,GAAiB,CAAxC,EAA2C;AACvC,iBAAKC,UAAL,CAAgBX,QAAhB,EAA0BK,MAA1B,EAAkCE,MAAlC,EAA0CR,OAAO,CAACW,MAAlD;AACH,WAdwE,CAgBzE;;;AACA,cAAI,KAAKxB,OAAL,IAAgBa,OAAO,CAACa,GAAR,GAAc,CAAlC,EAAqC;AACjC,iBAAKC,gBAAL,CAAsBb,QAAtB,EAAgCD,OAAhC,EAAyCM,MAAzC,EAAiDE,MAAjD;AACH,WAnBwE,CAqBzE;;;AACA,cAAI,KAAKvB,cAAL,IAAuBe,OAAO,CAACe,KAAR,GAAgB,CAA3C,EAA8C;AAC1C,iBAAKC,cAAL,CAAoBf,QAApB,EAA8BD,OAA9B,EAAuCM,MAAvC,EAA+CE,MAA/C;AACH;AACJ;;AAEOE,QAAAA,UAAU,CAACT,QAAD,EAAqBgB,MAArB,EAAqCC,MAArC,EAA2D;AACzE;AAAA;AAAA,wCAAWC,SAAX,CAAqBlB,QAArB,EAA+BgB,MAA/B,EAAuCC,MAAvC,EAA+C,KAAKpB,UAApD,EAAgE,KAAKN,WAArE;AACH;;AAEOoB,QAAAA,UAAU,CAACX,QAAD,EAAqBgB,MAArB,EAAqCC,MAArC,EAAqDP,MAArD,EAA2E;AACzF;AAAA;AAAA,wCAAWS,UAAX,CAAsBnB,QAAtB,EAAgCgB,MAAhC,EAAwCC,MAAxC,EAAgDP,MAAhD,EAAwD,KAAKvB,WAA7D,EAA0E,KAA1E;AACH;;AAEO0B,QAAAA,gBAAgB,CAACb,QAAD,EAAqBD,OAArB,EAA0CiB,MAA1C,EAA0DC,MAA1D,EAAgF;AACpG,cAAIlB,OAAO,CAACa,GAAR,IAAe,CAAnB,EAAsB;AAEtBZ,UAAAA,QAAQ,CAACoB,WAAT,GAAuB,KAAK3B,QAA5B;AACAO,UAAAA,QAAQ,CAACqB,SAAT,GAAqB,CAArB,CAJoG,CAMpG;AACA;;AACA,cAAMC,YAAY,GAAGvB,OAAO,CAACwB,KAAR,GAAgBC,IAAI,CAACC,EAArB,GAA0B,GAA/C;AACA,cAAMC,MAAM,GAAG3B,OAAO,CAACa,GAAR,GAAcY,IAAI,CAACC,EAAnB,GAAwB,GAAvC;AAEA,cAAME,UAAU,GAAGL,YAAY,GAAGI,MAAM,GAAG,CAA3C;AACA,cAAME,QAAQ,GAAGN,YAAY,GAAGI,MAAM,GAAG,CAAzC,CAZoG,CAcpG;;AACA,cAAMG,cAAc,GAAG9B,OAAO,CAACW,MAA/B,CAfoG,CAe7D;AACvC;;AACA,cAAMoB,UAAU,GAAG,EAAnB;AACA,cAAMC,WAAW,GAAGhC,OAAO,CAACiC,eAAR,IAA2B,CAA/C;AACA,cAAMC,SAAS,GAAGT,IAAI,CAACU,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKpC,UAArD,CAAlB;AACA,cAAMwC,YAAY,GAAGN,cAAc,GAAGI,SAAtC,CApBoG,CAsBpG;;AACA,cAAMG,QAAQ,GAAGZ,IAAI,CAACU,GAAL,CAAS,CAAT,EAAYV,IAAI,CAACa,KAAL,CAAWtC,OAAO,CAACa,GAAR,GAAc,CAAzB,CAAZ,CAAjB,CAvBoG,CAuBzC;AAE3D;;AACA,eAAK,IAAI0B,CAAC,GAAG,CAAb,EAAgBA,CAAC,IAAIF,QAArB,EAA+BE,CAAC,EAAhC,EAAoC;AAChC,gBAAMf,KAAK,GAAGI,UAAU,GAAG,CAACC,QAAQ,GAAGD,UAAZ,KAA2BW,CAAC,GAAGF,QAA/B,CAA3B;AACA,gBAAM9B,CAAC,GAAGU,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAAShB,KAAT,IAAkBY,YAArC;AACA,gBAAM3B,CAAC,GAAGS,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASjB,KAAT,IAAkBY,YAArC;;AAEA,gBAAIG,CAAC,KAAK,CAAV,EAAa;AACTtC,cAAAA,QAAQ,CAACyC,MAAT,CAAgBnC,CAAhB,EAAmBE,CAAnB;AACH,aAFD,MAEO;AACHR,cAAAA,QAAQ,CAAC0C,MAAT,CAAgBpC,CAAhB,EAAmBE,CAAnB;AACH;AACJ,WApCmG,CAsCpG;;;AACA,cAAMmC,WAAW,GAAG3B,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAASZ,UAAT,IAAuBE,cAApD;AACA,cAAMe,WAAW,GAAG3B,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASb,UAAT,IAAuBE,cAApD;AACA,cAAMgB,SAAS,GAAG7B,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAASX,QAAT,IAAqBC,cAAhD;AACA,cAAMiB,SAAS,GAAG7B,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASZ,QAAT,IAAqBC,cAAhD;AAEA,cAAMkB,SAAS,GAAG/B,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAASZ,UAAT,IAAuBQ,YAAlD;AACA,cAAMa,SAAS,GAAG/B,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASb,UAAT,IAAuBQ,YAAlD;AACA,cAAMc,OAAO,GAAGjC,MAAM,GAAGQ,IAAI,CAACe,GAAL,CAASX,QAAT,IAAqBO,YAA9C;AACA,cAAMe,OAAO,GAAGjC,MAAM,GAAGO,IAAI,CAACgB,GAAL,CAASZ,QAAT,IAAqBO,YAA9C,CA/CoG,CAiDpG;;AACAnC,UAAAA,QAAQ,CAACyC,MAAT,CAAgBE,WAAhB,EAA6BC,WAA7B;AACA5C,UAAAA,QAAQ,CAAC0C,MAAT,CAAgBK,SAAhB,EAA2BC,SAA3B;AACAhD,UAAAA,QAAQ,CAACyC,MAAT,CAAgBI,SAAhB,EAA2BC,SAA3B;AACA9C,UAAAA,QAAQ,CAAC0C,MAAT,CAAgBO,OAAhB,EAAyBC,OAAzB;AAEAlD,UAAAA,QAAQ,CAACmD,MAAT;AACH;;AAEOpC,QAAAA,cAAc,CAACf,QAAD,EAAqBD,OAArB,EAA0CiB,MAA1C,EAA0DC,MAA1D,EAAgF;AAClG,cAAMa,UAAU,GAAG,EAAnB;AACA,cAAMC,WAAW,GAAGhC,OAAO,CAACiC,eAAR,IAA2B,CAA/C;AACA,cAAMoB,WAAW,GAAG5B,IAAI,CAACU,GAAL,CAASJ,UAAT,EAAqBA,UAAU,GAAGC,WAAb,GAA2B,KAAKpC,UAArD,CAApB;;AAEA,eAAK,IAAI2C,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvC,OAAO,CAACe,KAA5B,EAAmCwB,CAAC,EAApC,EAAwC;AACpC,gBAAMe,SAAS,GAAGtD,OAAO,CAACuD,YAAR,CAAqBhB,CAArB,CAAlB;AACA,gBAAMiB,QAAQ,GAAGxD,OAAO,CAACyD,gBAAR,CAAyBlB,CAAzB,CAAjB,CAFoC,CAIpC;;AACA,gBAAMmB,MAAM,GAAGzC,MAAM,GAAGuC,QAAQ,CAACjD,CAAjC;AACA,gBAAMoD,MAAM,GAAGzC,MAAM,GAAGsC,QAAQ,CAAC/C,CAAjC,CANoC,CAQpC;;AACA,gBAAMmD,IAAI,GAAGF,MAAM,GAAGJ,SAAS,CAAC/C,CAAV,GAAc8C,WAApC;AACA,gBAAMQ,IAAI,GAAGF,MAAM,GAAGL,SAAS,CAAC7C,CAAV,GAAc4C,WAApC,CAVoC,CAYpC;;AACA;AAAA;AAAA,0CAAWS,SAAX,CAAqB7D,QAArB,EAA+ByD,MAA/B,EAAuCC,MAAvC,EAA+CC,IAA/C,EAAqDC,IAArD,EAA2D,KAAKvE,cAAhE,EAAgF,KAAKO,SAArF;AACH;AACJ;;AAEMkE,QAAAA,WAAW,GAAW;AACzB,iBAAO,EAAP,CADyB,CACd;AACd;AAED;AACJ;AACA;;;AACWC,QAAAA,SAAS,CAACC,OAAD,EAYP;AACL,cAAIA,OAAO,CAACjF,UAAR,KAAuBkF,SAA3B,EAAsC,KAAKlF,UAAL,GAAkBiF,OAAO,CAACjF,UAA1B;AACtC,cAAIiF,OAAO,CAAChF,cAAR,KAA2BiF,SAA/B,EAA0C,KAAKjF,cAAL,GAAsBgF,OAAO,CAAChF,cAA9B;AAC1C,cAAIgF,OAAO,CAAC/E,UAAR,KAAuBgF,SAA3B,EAAsC,KAAKhF,UAAL,GAAkB+E,OAAO,CAAC/E,UAA1B;AACtC,cAAI+E,OAAO,CAAC9E,OAAR,KAAoB+E,SAAxB,EAAmC,KAAK/E,OAAL,GAAe8E,OAAO,CAAC9E,OAAvB;AACnC,cAAI8E,OAAO,CAAC7E,WAAR,KAAwB8E,SAA5B,EAAuC,KAAK9E,WAAL,GAAmB6E,OAAO,CAAC7E,WAA3B;AACvC,cAAI6E,OAAO,CAAC3E,cAAR,KAA2B4E,SAA/B,EAA0C,KAAK5E,cAAL,GAAsB2E,OAAO,CAAC3E,cAA9B;AAC1C,cAAI2E,OAAO,CAACzE,WAAR,KAAwB0E,SAA5B,EAAuC,KAAK1E,WAAL,GAAmByE,OAAO,CAACzE,WAA3B;AACvC,cAAIyE,OAAO,CAACvE,QAAR,KAAqBwE,SAAzB,EAAoC,KAAKxE,QAAL,GAAgBuE,OAAO,CAACvE,QAAxB;AACpC,cAAIuE,OAAO,CAACrE,UAAR,KAAuBsE,SAA3B,EAAsC,KAAKtE,UAAL,GAAkBqE,OAAO,CAACrE,UAA1B;AACtC,cAAIqE,OAAO,CAACpE,SAAR,KAAsBqE,SAA1B,EAAqC,KAAKrE,SAAL,GAAiBoE,OAAO,CAACpE,SAAzB;AACrC,cAAIoE,OAAO,CAACnE,UAAR,KAAuBoE,SAA3B,EAAsC,KAAKpE,UAAL,GAAkBmE,OAAO,CAACnE,UAA1B;AACzC;;AAxKwD,O", "sourcesContent": ["import { _decorator, Graphics, Color, Node, Vec3 } from 'cc';\nimport { GizmoDrawer, RegisterGizmoDrawer } from './GizmoDrawer';\nimport { GizmoUtils } from './GizmoUtils';\nimport { EmitterArc } from '../world/weapon/EmitterArc';\n\n/**\n * Gizmo drawer for EmitterArc components\n * Draws visual debugging information for arc-based bullet emitters\n */\n@RegisterGizmoDrawer\nexport class EmitterArcGizmo extends GizmoDrawer<EmitterArc> {\n    \n    public readonly componentType = EmitterArc;\n    public readonly drawerName = \"EmitterArcGizmo\";\n    \n    // Gizmo display options\n    public showRadius: boolean = true;\n    public showDirections: boolean = true;\n    public showCenter: boolean = true;\n    public showArc: boolean = true;\n    \n    // Colors\n    public radiusColor: Color = Color.GRAY;\n    public directionColor: Color = Color.RED;\n    public centerColor: Color = Color.WHITE;\n    public arcColor: Color = Color.YELLOW;\n    \n    // Display settings\n    public speedScale: number = 1.0;\n    public arrowSize: number = 8;\n    public centerSize: number = 8;\n    \n    public drawGizmos(emitter: EmitterArc, graphics: Graphics, node: Node): void {\n        // For 2D projects, convert world position to graphics local space\n        const gizmoPos = this.worldToGizmoSpace(node.worldPosition, graphics.node);\n        const gizmoX = gizmoPos.x;\n        const gizmoY = gizmoPos.y;\n\n        // Draw center point\n        if (this.showCenter) {\n            this.drawCenter(graphics, gizmoX, gizmoY);\n        }\n\n        // Draw radius circle\n        if (this.showRadius && emitter.radius > 0) {\n            this.drawRadius(graphics, gizmoX, gizmoY, emitter.radius);\n        }\n\n        // Draw arc indicator\n        if (this.showArc && emitter.arc > 0) {\n            this.drawArcIndicator(graphics, emitter, gizmoX, gizmoY);\n        }\n\n        // Draw direction arrows\n        if (this.showDirections && emitter.count > 0) {\n            this.drawDirections(graphics, emitter, gizmoX, gizmoY);\n        }\n    }\n    \n    private drawCenter(graphics: Graphics, worldX: number, worldY: number): void {\n        GizmoUtils.drawCross(graphics, worldX, worldY, this.centerSize, this.centerColor);\n    }\n\n    private drawRadius(graphics: Graphics, worldX: number, worldY: number, radius: number): void {\n        GizmoUtils.drawCircle(graphics, worldX, worldY, radius, this.radiusColor, false);\n    }\n    \n    private drawArcIndicator(graphics: Graphics, emitter: EmitterArc, worldX: number, worldY: number): void {\n        if (emitter.arc <= 0) return;\n\n        graphics.strokeColor = this.arcColor;\n        graphics.lineWidth = 2;\n\n        // Convert angle and arc to radians\n        // Use the same coordinate system as EmitterArc.getDirection() - no +90 offset\n        const baseAngleRad = emitter.angle * Math.PI / 180;\n        const arcRad = emitter.arc * Math.PI / 180;\n\n        const startAngle = baseAngleRad - arcRad / 2;\n        const endAngle = baseAngleRad + arcRad / 2;\n\n        // Draw arc starting from the emitter radius (spawn position) extending outward\n        const arcStartRadius = emitter.radius; // Start from spawn radius\n        // Use same length calculation as direction arrows for consistency\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arcLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n        const arcEndRadius = arcStartRadius + arcLength;\n\n        // Draw arc lines from spawn radius extending outward to show direction range\n        const segments = Math.max(8, Math.floor(emitter.arc / 5)); // More segments for larger arcs\n\n        // Draw the arc at the end radius to show the direction spread\n        for (let i = 0; i <= segments; i++) {\n            const angle = startAngle + (endAngle - startAngle) * (i / segments);\n            const x = worldX + Math.cos(angle) * arcEndRadius;\n            const y = worldY + Math.sin(angle) * arcEndRadius;\n\n            if (i === 0) {\n                graphics.moveTo(x, y);\n            } else {\n                graphics.lineTo(x, y);\n            }\n        }\n\n        // Draw lines from spawn position to end of arc to show the direction range\n        const startSpawnX = worldX + Math.cos(startAngle) * arcStartRadius;\n        const startSpawnY = worldY + Math.sin(startAngle) * arcStartRadius;\n        const endSpawnX = worldX + Math.cos(endAngle) * arcStartRadius;\n        const endSpawnY = worldY + Math.sin(endAngle) * arcStartRadius;\n\n        const startEndX = worldX + Math.cos(startAngle) * arcEndRadius;\n        const startEndY = worldY + Math.sin(startAngle) * arcEndRadius;\n        const endEndX = worldX + Math.cos(endAngle) * arcEndRadius;\n        const endEndY = worldY + Math.sin(endAngle) * arcEndRadius;\n\n        // Draw lines from spawn radius to end radius for arc boundaries\n        graphics.moveTo(startSpawnX, startSpawnY);\n        graphics.lineTo(startEndX, startEndY);\n        graphics.moveTo(endSpawnX, endSpawnY);\n        graphics.lineTo(endEndX, endEndY);\n\n        graphics.stroke();\n    }\n    \n    private drawDirections(graphics: Graphics, emitter: EmitterArc, worldX: number, worldY: number): void {\n        const baseLength = 30;\n        const speedFactor = emitter.speedMultiplier || 1;\n        const arrowLength = Math.max(baseLength, baseLength * speedFactor * this.speedScale);\n\n        for (let i = 0; i < emitter.count; i++) {\n            const direction = emitter.getDirection(i);\n            const spawnPos = emitter.getSpawnPosition(i);\n\n            // Start position (at spawn position relative to world position)\n            const startX = worldX + spawnPos.x;\n            const startY = worldY + spawnPos.y;\n\n            // End position (direction from spawn position)\n            const endX = startX + direction.x * arrowLength;\n            const endY = startY + direction.y * arrowLength;\n\n            // Draw arrow\n            GizmoUtils.drawArrow(graphics, startX, startY, endX, endY, this.directionColor, this.arrowSize);\n        }\n    }\n    \n    public getPriority(): number {\n        return 10; // Draw emitter gizmos with medium priority\n    }\n    \n    /**\n     * Configure display options\n     */\n    public configure(options: {\n        showRadius?: boolean;\n        showDirections?: boolean;\n        showCenter?: boolean;\n        showArc?: boolean;\n        radiusColor?: Color;\n        directionColor?: Color;\n        centerColor?: Color;\n        arcColor?: Color;\n        speedScale?: number;\n        arrowSize?: number;\n        centerSize?: number;\n    }): void {\n        if (options.showRadius !== undefined) this.showRadius = options.showRadius;\n        if (options.showDirections !== undefined) this.showDirections = options.showDirections;\n        if (options.showCenter !== undefined) this.showCenter = options.showCenter;\n        if (options.showArc !== undefined) this.showArc = options.showArc;\n        if (options.radiusColor !== undefined) this.radiusColor = options.radiusColor;\n        if (options.directionColor !== undefined) this.directionColor = options.directionColor;\n        if (options.centerColor !== undefined) this.centerColor = options.centerColor;\n        if (options.arcColor !== undefined) this.arcColor = options.arcColor;\n        if (options.speedScale !== undefined) this.speedScale = options.speedScale;\n        if (options.arrowSize !== undefined) this.arrowSize = options.arrowSize;\n        if (options.centerSize !== undefined) this.centerSize = options.centerSize;\n    }\n}\n"]}