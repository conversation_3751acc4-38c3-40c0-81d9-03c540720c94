{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAigC,uCAAjgC,EAAilC,uCAAjlC,EAAspC,uCAAtpC,EAAguC,wCAAhuC,EAA6yC,wCAA7yC,EAAw3C,wCAAx3C,EAA87C,wCAA97C,EAA0gD,wCAA1gD,EAAilD,wCAAjlD,EAA8pD,wCAA9pD,EAAwuD,wCAAxuD,EAAyzD,wCAAzzD,EAAi4D,wCAAj4D,EAAw8D,wCAAx8D,EAAkhE,wCAAlhE,EAAwlE,wCAAxlE,EAAgqE,wCAAhqE,EAA8uE,wCAA9uE,EAAm0E,wCAAn0E,EAA+5E,wCAA/5E,EAAq/E,wCAAr/E,EAA0kF,wCAA1kF,EAAgqF,wCAAhqF,EAA6vF,wCAA7vF,EAAq1F,wCAAr1F,EAAy6F,wCAAz6F,EAA8/F,wCAA9/F,EAAilG,wCAAjlG,EAAmqG,wCAAnqG,EAAsvG,wCAAtvG,EAAo0G,wCAAp0G,EAAq5G,wCAAr5G,EAAg/G,wCAAh/G,EAAmkH,wCAAnkH,EAAspH,wCAAtpH,EAAkvH,wCAAlvH,EAAq0H,wCAAr0H,EAAu5H,wCAAv5H,EAAo+H,wCAAp+H,EAA6jI,wCAA7jI,EAA2pI,wCAA3pI,EAAwvI,wCAAxvI,EAAs1I,wCAAt1I,EAAk7I,wCAAl7I,EAA2gJ,wCAA3gJ,EAAsmJ,wCAAtmJ,EAA2rJ,wCAA3rJ,EAAsxJ,wCAAtxJ,EAA42J,wCAA52J,EAAq8J,wCAAr8J,EAA0hK,wCAA1hK,EAAmnK,wCAAnnK,EAA4rK,wCAA5rK,EAA6vK,wCAA7vK,EAAw0K,wCAAx0K,EAA24K,wCAA34K,EAA49K,wCAA59K,EAAuiL,wCAAviL,EAAunL,wCAAvnL,EAAysL,wCAAzsL,EAA4xL,wCAA5xL,EAA+3L,wCAA/3L,EAAk+L,wCAAl+L,EAA6jM,wCAA7jM,EAA4pM,wCAA5pM,EAAiwM,wCAAjwM,EAA02M,wCAA12M,EAAo7M,wCAAp7M,EAA6/M,wCAA7/M,EAA0kN,wCAA1kN,EAAupN,wCAAvpN,EAAmuN,wCAAnuN,EAA8yN,wCAA9yN,EAA83N,wCAA93N,EAA28N,wCAA38N,EAAqhO,wCAArhO,EAA0lO,wCAA1lO,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/Bag.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/BaseInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/DataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Data/GameLevel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Anim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Background.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Enemy.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/EnemyBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameOver.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GamePersistNode.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Global.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Goods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/MainGame.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Menu.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/Player.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/PlayerBullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/AnimFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/EnemyFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/GameFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/GoodsFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/factroy/PlayerBulletFactory.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/gizmos/EmitterArcGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoDrawer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/gizmos/GizmoUtils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PathGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/gizmos/PointGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/gizmos/index.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/Bootstrap.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/WorldInitializeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/Object.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/System.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/SystemContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/TypeID.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/base/World.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/index.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/LevelBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/LevelSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/player/PlayerSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/EmitterArc.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/Weapon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/weapon/WeaponSlot.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/GameInstance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/DevLoginData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/ButtonPlus.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/Button/DragButton.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/List.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/List/ListItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelect.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Components/Common/SelectList/uiSelectItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/DevLoginUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/LoadingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Main/BattleUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Main/BottomUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Main/PlaneUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Main/ShopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Main/SkyIslandUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Main/TalentUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/Main/TopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/UI/UIMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}