System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, PathPoint, PointBaker, PathBaked, _dec, _dec2, _class2, _class3, _descriptor, _crd, ccclass, property, PathBaker;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfIBaker(extras) {
    _reporterNs.report("IBaker", "./Baker", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIBaked(extras) {
    _reporterNs.report("IBaked", "./Baker", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPathPoint(extras) {
    _reporterNs.report("PathPoint", "../Data/PathPoint", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPointBaker(extras) {
    _reporterNs.report("PointBaker", "./PointBaker", _context.meta, extras);
  }

  _export("PathBaked", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      PathPoint = _unresolved_2.PathPoint;
    }, function (_unresolved_3) {
      PointBaker = _unresolved_3.PointBaker;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "3ffb0u2H0hPypXgQ29n+XYm", "PathBaker", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'ValueType']);

      ({
        ccclass,
        property
      } = _decorator);

      // TODO: 导出json
      _export("PathBaked", PathBaked = class PathBaked {
        constructor() {
          this.points = void 0;
        }

        addPoint(x, y, isOnPath) {
          if (!this.points) {
            this.points = [];
          }

          this.points.push(new (_crd && PathPoint === void 0 ? (_reportPossibleCrUseOfPathPoint({
            error: Error()
          }), PathPoint) : PathPoint)(x, y, isOnPath));
        }

        removePoint(index) {
          if (this.points && index >= 0 && index < this.points.length) {
            this.points.splice(index, 1);
          }
        }

        clearPoints() {
          this.points = [];
        }

      });

      _export("PathBaker", PathBaker = (_dec = ccclass('PathBaker'), _dec2 = property({
        type: [_crd && PointBaker === void 0 ? (_reportPossibleCrUseOfPointBaker({
          error: Error()
        }), PointBaker) : PointBaker],
        serializable: true
      }), _dec(_class2 = (_class3 = class PathBaker extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "points", _descriptor, this);
        }

        bake() {
          var baked = new PathBaked();
          this.points.forEach(point => {
            var pointData = point.bake(); // Ensure pointData is of type PathPoint

            baked.addPoint(pointData.x, pointData.y, pointData.isOnPath);
          });
          return baked;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class3.prototype, "points", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class3)) || _class2));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=da3a5aac5f0797562e64a16aa877d1f5f0f5ef64.js.map