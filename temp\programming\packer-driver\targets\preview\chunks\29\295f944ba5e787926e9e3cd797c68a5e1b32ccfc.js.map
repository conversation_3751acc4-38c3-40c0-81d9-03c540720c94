{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts"], "names": ["_decorator", "CCFloat", "CCBoolean", "ccclass", "property", "PathPoint", "type", "constructor", "x", "y", "isOnPath"], "mappings": ";;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;;;;;;;;;OACxB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBJ,U,GAE9B;;2BAEaK,S,WACRD,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL;AAAR,OAAD,C,UAERG,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEL;AAAR,OAAD,C,UAERG,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEJ;AAAR,OAAD,C,EANZC,O,qBAAD,MACaE,SADb,CACuB;AAQnBE,QAAAA,WAAW,CAACC,CAAD,EAAYC,CAAZ,EAAuBC,QAAvB,EAA0C;AAAA;;AAAA;;AAAA;;AACjD,eAAKF,CAAL,GAASA,CAAT;AACA,eAAKC,CAAL,GAASA,CAAT;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACH;;AAZkB,O", "sourcesContent": ["\r\nimport { _decorator, CCFloat, CCBoolean } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n// define a point struct with x, y and boolean isOnPath\r\n@ccclass\r\nexport class PathPoint {\r\n    @property({ type: CCFloat })\r\n    public x: number;\r\n    @property({ type: CCFloat })\r\n    public y: number;\r\n    @property({ type: CCBoolean })\r\n    public isOnPath: boolean;\r\n\r\n    constructor(x: number, y: number, isOnPath: boolean) {\r\n        this.x = x;\r\n        this.y = y;\r\n        this.isOnPath = isOnPath;\r\n    }\r\n}"]}