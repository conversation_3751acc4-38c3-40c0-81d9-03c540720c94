import { _decorator, Component, Node, ValueType } from 'cc';
const { ccclass, property } = _decorator;
import { IBaker, IBaked } from './Baker';
import { PathPoint } from '../Data/PathPoint';
import { PointBaker } from './PointBaker';

// TODO: 导出json
export class PathBaked implements IBaked {
    points: PathPoint[];

    addPoint(x: number, y: number, isOnPath: boolean) {
        if (!this.points) {
            this.points = [];
        }
        this.points.push(new PathPoint(x, y, isOnPath));
    }

    removePoint(index: number) {
        if (this.points && index >= 0 && index < this.points.length) {
            this.points.splice(index, 1);
        }
    }

    clearPoints() {
        this.points = [];
    }
}

@ccclass('PathBaker')
export class PathBaker extends Component implements IBaker 
{
    @property({ type: [PointBaker], serializable: true })
    public points: PointBaker[] = [];

    bake(): IBaked {
        const baked = new PathBaked();
        this.points.forEach(point => {
            let pointData = point.bake() as PathPoint;
            // Ensure pointData is of type PathPoint
            baked.addPoint(pointData.x, pointData.y, pointData.isOnPath);
        });
        return baked;
    }
}


